-- Migration: Thê<PERSON> các trường quản lý tồn kho cho bảng user_products và inventory
-- Date: 2025-01-02
-- Description: Thêm SKU, Barcode cho user_products và cải thiện bảng inventory

-- 1. Thêm SKU và Barcode vào bảng user_products
ALTER TABLE user_products 
ADD COLUMN IF NOT EXISTS sku VARCHAR(100) NULL COMMENT 'Mã SKU của sản phẩm',
ADD COLUMN IF NOT EXISTS barcode VARCHAR(100) NULL COMMENT 'Mã vạch của sản phẩm';

-- Tạo index cho SKU và Barcode để tìm kiếm nhanh
CREATE INDEX IF NOT EXISTS idx_user_products_sku ON user_products(sku);
CREATE INDEX IF NOT EXISTS idx_user_products_barcode ON user_products(barcode);
CREATE INDEX IF NOT EXISTS idx_user_products_user_sku ON user_products(user_id, sku);
CREATE INDEX IF NOT EXISTS idx_user_products_user_barcode ON user_products(user_id, barcode);

-- 2. <PERSON><PERSON><PERSON> thiện bảng inventory (nếu chưa có đầy đủ)
-- Kiểm tra và tạo bảng inventory nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS inventory (
    id BIGSERIAL PRIMARY KEY COMMENT 'ID tự tăng của bản ghi tồn kho',
    product_id BIGINT NOT NULL COMMENT 'ID sản phẩm từ bảng user_products',
    warehouse_id INTEGER NULL COMMENT 'ID kho vật lý từ bảng physical_warehouse',
    sku VARCHAR(100) NULL COMMENT 'Mã SKU của sản phẩm (copy từ user_products)',
    barcode VARCHAR(100) NULL COMMENT 'Mã vạch của sản phẩm (copy từ user_products)',
    current_quantity INTEGER NOT NULL DEFAULT 0 COMMENT 'Số lượng hiện tại trong kho',
    total_quantity INTEGER NOT NULL DEFAULT 0 COMMENT 'Tổng số lượng đã nhập vào kho',
    available_quantity INTEGER NOT NULL DEFAULT 0 COMMENT 'Số lượng sẵn sàng để bán',
    reserved_quantity INTEGER NOT NULL DEFAULT 0 COMMENT 'Số lượng bị giữ chỗ',
    defective_quantity INTEGER NOT NULL DEFAULT 0 COMMENT 'Số lượng sản phẩm lỗi',
    last_updated BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()) * 1000 COMMENT 'Thời gian cập nhật cuối (timestamp)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian tạo bản ghi',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời gian cập nhật bản ghi'
);

-- Thêm các trường mới vào bảng inventory nếu chưa có
ALTER TABLE inventory 
ADD COLUMN IF NOT EXISTS sku VARCHAR(100) NULL COMMENT 'Mã SKU của sản phẩm (copy từ user_products)',
ADD COLUMN IF NOT EXISTS barcode VARCHAR(100) NULL COMMENT 'Mã vạch của sản phẩm (copy từ user_products)';

-- 3. Tạo các index cho bảng inventory
CREATE INDEX IF NOT EXISTS idx_inventory_product_id ON inventory(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_warehouse_id ON inventory(warehouse_id);
CREATE INDEX IF NOT EXISTS idx_inventory_sku ON inventory(sku);
CREATE INDEX IF NOT EXISTS idx_inventory_barcode ON inventory(barcode);
CREATE INDEX IF NOT EXISTS idx_inventory_product_warehouse ON inventory(product_id, warehouse_id);
CREATE INDEX IF NOT EXISTS idx_inventory_sku_warehouse ON inventory(sku, warehouse_id);

-- 4. Tạo constraint để đảm bảo tính toàn vẹn dữ liệu
-- Đảm bảo current_quantity = available_quantity + reserved_quantity + defective_quantity
ALTER TABLE inventory 
ADD CONSTRAINT IF NOT EXISTS chk_inventory_quantity_balance 
CHECK (current_quantity = available_quantity + reserved_quantity + defective_quantity);

-- Đảm bảo các số lượng không âm
ALTER TABLE inventory 
ADD CONSTRAINT IF NOT EXISTS chk_inventory_quantities_positive 
CHECK (
    current_quantity >= 0 AND 
    total_quantity >= 0 AND 
    available_quantity >= 0 AND 
    reserved_quantity >= 0 AND 
    defective_quantity >= 0
);

-- 5. Tạo unique constraint cho product_id + warehouse_id
-- Mỗi sản phẩm chỉ có một bản ghi tồn kho trong một kho
ALTER TABLE inventory 
ADD CONSTRAINT IF NOT EXISTS uk_inventory_product_warehouse 
UNIQUE (product_id, warehouse_id);

-- 6. Tạo foreign key constraints (nếu chưa có)
-- Lưu ý: Chỉ tạo nếu các bảng tham chiếu đã tồn tại
-- ALTER TABLE inventory 
-- ADD CONSTRAINT IF NOT EXISTS fk_inventory_product 
-- FOREIGN KEY (product_id) REFERENCES user_products(id) ON DELETE CASCADE;

-- ALTER TABLE inventory 
-- ADD CONSTRAINT IF NOT EXISTS fk_inventory_warehouse 
-- FOREIGN KEY (warehouse_id) REFERENCES physical_warehouse(warehouse_id) ON DELETE SET NULL;

-- 7. Tạo trigger để tự động cập nhật SKU và Barcode trong inventory khi user_products thay đổi
DELIMITER $$

CREATE TRIGGER IF NOT EXISTS tr_update_inventory_sku_barcode
AFTER UPDATE ON user_products
FOR EACH ROW
BEGIN
    -- Cập nhật SKU và Barcode trong inventory khi user_products thay đổi
    IF OLD.sku != NEW.sku OR OLD.barcode != NEW.barcode THEN
        UPDATE inventory 
        SET 
            sku = NEW.sku,
            barcode = NEW.barcode,
            updated_at = CURRENT_TIMESTAMP
        WHERE product_id = NEW.id;
    END IF;
END$$

DELIMITER ;

-- 8. Tạo trigger để tự động cập nhật last_updated khi inventory thay đổi
DELIMITER $$

CREATE TRIGGER IF NOT EXISTS tr_update_inventory_timestamp
BEFORE UPDATE ON inventory
FOR EACH ROW
BEGIN
    SET NEW.last_updated = EXTRACT(EPOCH FROM NOW()) * 1000;
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

DELIMITER ;

-- 9. Cập nhật dữ liệu hiện có (nếu có)
-- Đồng bộ SKU và Barcode từ user_products sang inventory
UPDATE inventory i
INNER JOIN user_products up ON i.product_id = up.id
SET 
    i.sku = up.sku,
    i.barcode = up.barcode,
    i.updated_at = CURRENT_TIMESTAMP
WHERE i.sku IS NULL OR i.barcode IS NULL OR i.sku != up.sku OR i.barcode != up.barcode;

-- 10. Tạo view để dễ dàng truy vấn thông tin tồn kho đầy đủ
CREATE OR REPLACE VIEW v_product_inventory AS
SELECT 
    i.id as inventory_id,
    i.product_id,
    up.name as product_name,
    up.sku,
    up.barcode,
    i.warehouse_id,
    pw.address as warehouse_address,
    w.name as warehouse_name,
    w.type as warehouse_type,
    pw.capacity as warehouse_capacity,
    i.current_quantity,
    i.total_quantity,
    i.available_quantity,
    i.reserved_quantity,
    i.defective_quantity,
    i.last_updated,
    i.created_at,
    i.updated_at,
    up.user_id
FROM inventory i
INNER JOIN user_products up ON i.product_id = up.id
LEFT JOIN physical_warehouse pw ON i.warehouse_id = pw.warehouse_id
LEFT JOIN warehouse w ON i.warehouse_id = w.warehouse_id
WHERE up.status = 'ACTIVE';

-- Thêm comment cho view
COMMENT ON VIEW v_product_inventory IS 'View tổng hợp thông tin tồn kho sản phẩm với thông tin kho và sản phẩm';
