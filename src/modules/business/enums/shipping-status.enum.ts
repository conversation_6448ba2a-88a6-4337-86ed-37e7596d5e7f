/**
 * Enum định nghĩa các trạng thái giao hàng
 */
export enum ShippingStatusEnum {
  /**
   * Chờ xử lý - Đơn hàng vừa được tạo
   */
  PENDING = 'pending',

  /**
   * Đang xử lý - Đơn hàng đang được chuẩn bị
   */
  PROCESSING = 'processing',

  /**
   * Chờ lấy hàng - Đơn hàng đã sẵn sàng để shipper lấy
   */
  READY_TO_PICK = 'ready_to_pick',

  /**
   * Đang lấy hàng - Shipper đang đến lấy hàng
   */
  PICKING = 'picking',

  /**
   * Đã lấy hàng - Shipper đã lấy hàng thành công
   */
  PICKED = 'picked',

  /**
   * Đang lưu kho - Hàng đã về kho trung chuyển
   */
  STORING = 'storing',

  /**
   * Đang vận chuyển - Hàng đang được vận chuyển giữa các kho
   */
  TRANSPORTING = 'transporting',

  /**
   * Đang phân loại - Hàng đang được phân loại tại kho
   */
  SORTING = 'sorting',

  /**
   * Đang giao hàng - Shipper đang giao hàng cho người nhận
   */
  DELIVERING = 'delivering',

  /**
   * Đã giao hàng - Giao hàng thành công
   */
  DELIVERED = 'delivered',

  /**
   * Giao hàng thất bại - Không thể giao hàng
   */
  DELIVERY_FAILED = 'delivery_failed',

  /**
   * Đã hủy - Đơn hàng bị hủy
   */
  CANCELLED = 'cancelled',

  /**
   * Đang hoàn trả - Hàng đang được hoàn trả về người gửi
   */
  RETURNING = 'returning',

  /**
   * Đã hoàn trả - Hàng đã được hoàn trả thành công
   */
  RETURNED = 'returned',

  /**
   * Thất lạc - Hàng bị thất lạc trong quá trình vận chuyển
   */
  LOST = 'lost',

  /**
   * Hư hỏng - Hàng bị hư hỏng
   */
  DAMAGED = 'damaged'
}

/**
 * Mapping trạng thái từ các nhà cung cấp vận chuyển về trạng thái chuẩn
 */
export const SHIPPING_STATUS_MAPPING = {
  // GHN Status Mapping
  GHN: {
    'ready_to_pick': ShippingStatusEnum.READY_TO_PICK,
    'picking': ShippingStatusEnum.PICKING,
    'picked': ShippingStatusEnum.PICKED,
    'storing': ShippingStatusEnum.STORING,
    'transporting': ShippingStatusEnum.TRANSPORTING,
    'sorting': ShippingStatusEnum.SORTING,
    'delivering': ShippingStatusEnum.DELIVERING,
    'delivered': ShippingStatusEnum.DELIVERED,
    'delivery_fail': ShippingStatusEnum.DELIVERY_FAILED,
    'cancel': ShippingStatusEnum.CANCELLED,
    'return': ShippingStatusEnum.RETURNING,
    'returned': ShippingStatusEnum.RETURNED,
    'exception': ShippingStatusEnum.LOST,
    'damage': ShippingStatusEnum.DAMAGED
  },

  // GHTK Status Mapping
  GHTK: {
    '1': ShippingStatusEnum.PENDING, // Chưa tiếp nhận
    '2': ShippingStatusEnum.PROCESSING, // Đã tiếp nhận
    '3': ShippingStatusEnum.PICKED, // Đã lấy hàng/Đã nhập kho
    '4': ShippingStatusEnum.TRANSPORTING, // Đã điều phối giao hàng/Đang luân chuyển
    '5': ShippingStatusEnum.DELIVERING, // Đang giao hàng
    '6': ShippingStatusEnum.DELIVERED, // Đã giao hàng/Chưa đối soát
    '7': ShippingStatusEnum.RETURNED, // Không lấy được hàng
    '8': ShippingStatusEnum.DELIVERY_FAILED, // Hoãn lấy hàng
    '9': ShippingStatusEnum.RETURNING, // Không giao được hàng
    '10': ShippingStatusEnum.DELIVERY_FAILED, // Delay giao hàng
    '11': ShippingStatusEnum.DELIVERED, // Đã đối soát
    '12': ShippingStatusEnum.RETURNED, // Đã trả hàng/Đã nhập kho
    '13': ShippingStatusEnum.CANCELLED, // Hủy đơn hàng
    '20': ShippingStatusEnum.STORING, // Đã nhập kho
    '21': ShippingStatusEnum.DELIVERING // Đã xuất kho đi giao
  },

  // J&T Status Mapping
  JT: {
    '100': ShippingStatusEnum.PENDING, // Order created
    '110': ShippingStatusEnum.PROCESSING, // Order confirmed
    '120': ShippingStatusEnum.READY_TO_PICK, // Ready for pickup
    '130': ShippingStatusEnum.PICKING, // Pickup in progress
    '140': ShippingStatusEnum.PICKED, // Picked up
    '200': ShippingStatusEnum.STORING, // In warehouse
    '300': ShippingStatusEnum.TRANSPORTING, // In transit
    '400': ShippingStatusEnum.DELIVERING, // Out for delivery
    '500': ShippingStatusEnum.DELIVERED, // Delivered
    '600': ShippingStatusEnum.DELIVERY_FAILED, // Delivery failed
    '700': ShippingStatusEnum.RETURNING, // Return in progress
    '800': ShippingStatusEnum.RETURNED, // Returned
    '900': ShippingStatusEnum.CANCELLED // Cancelled
  },

  // Ahamove Status Mapping
  AHAMOVE: {
    'IDLE': ShippingStatusEnum.PENDING,
    'ASSIGNING': ShippingStatusEnum.PROCESSING,
    'ACCEPTED': ShippingStatusEnum.READY_TO_PICK,
    'IN_PROGRESS': ShippingStatusEnum.PICKING,
    'COMPLETED': ShippingStatusEnum.DELIVERED,
    'CANCELLED': ShippingStatusEnum.CANCELLED,
    'FAILED': ShippingStatusEnum.DELIVERY_FAILED
  }
};

/**
 * Mô tả trạng thái giao hàng bằng tiếng Việt
 */
export const SHIPPING_STATUS_DESCRIPTIONS = {
  [ShippingStatusEnum.PENDING]: 'Chờ xử lý',
  [ShippingStatusEnum.PROCESSING]: 'Đang xử lý',
  [ShippingStatusEnum.READY_TO_PICK]: 'Chờ lấy hàng',
  [ShippingStatusEnum.PICKING]: 'Đang lấy hàng',
  [ShippingStatusEnum.PICKED]: 'Đã lấy hàng',
  [ShippingStatusEnum.STORING]: 'Đang lưu kho',
  [ShippingStatusEnum.TRANSPORTING]: 'Đang vận chuyển',
  [ShippingStatusEnum.SORTING]: 'Đang phân loại',
  [ShippingStatusEnum.DELIVERING]: 'Đang giao hàng',
  [ShippingStatusEnum.DELIVERED]: 'Đã giao hàng',
  [ShippingStatusEnum.DELIVERY_FAILED]: 'Giao hàng thất bại',
  [ShippingStatusEnum.CANCELLED]: 'Đã hủy',
  [ShippingStatusEnum.RETURNING]: 'Đang hoàn trả',
  [ShippingStatusEnum.RETURNED]: 'Đã hoàn trả',
  [ShippingStatusEnum.LOST]: 'Thất lạc',
  [ShippingStatusEnum.DAMAGED]: 'Hư hỏng'
};
