import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, Min, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo/cập nhật tồn kho sản phẩm từ user product
 */
export class ProductInventoryDto {
  /**
   * ID kho chứa sản phẩm
   * @example 1
   */
  @ApiProperty({
    description: 'ID kho chứa sản phẩm',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID kho phải là số' })
  @Type(() => Number)
  warehouseId?: number;

  /**
   * Số lượng sẵn sàng để bán hoặc sử dụng
   * @example 100
   */
  @ApiProperty({
    description: 'Số lượng sẵn sàng để bán hoặc sử dụng',
    example: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '<PERSON><PERSON> lượng sẵn sàng phải là số' })
  @Min(0, { message: '<PERSON><PERSON> lượng sẵn sàng phải lớn hơn hoặc bằng 0' })
  @Type(() => Number)
  availableQuantity?: number;

  /**
   * Số lượng bị giữ chỗ (ví dụ: cho đơn hàng chưa hoàn tất)
   * @example 10
   */
  @ApiProperty({
    description: 'Số lượng bị giữ chỗ (ví dụ: cho đơn hàng chưa hoàn tất)',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số lượng giữ chỗ phải là số' })
  @Min(0, { message: 'Số lượng giữ chỗ phải lớn hơn hoặc bằng 0' })
  @Type(() => Number)
  reservedQuantity?: number;

  /**
   * Số lượng sản phẩm lỗi hoặc không sử dụng được
   * @example 5
   */
  @ApiProperty({
    description: 'Số lượng sản phẩm lỗi hoặc không sử dụng được',
    example: 5,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số lượng lỗi phải là số' })
  @Min(0, { message: 'Số lượng lỗi phải lớn hơn hoặc bằng 0' })
  @Type(() => Number)
  defectiveQuantity?: number;

  /**
   * Mã SKU (Stock Keeping Unit) của sản phẩm trong kho
   * @example "SKU-001"
   */
  @ApiProperty({
    description: 'Mã SKU (Stock Keeping Unit) của sản phẩm trong kho',
    example: 'SKU-001',
    required: false,
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Mã SKU phải là chuỗi' })
  @MaxLength(100, { message: 'Mã SKU không được vượt quá 100 ký tự' })
  sku?: string;

  /**
   * Mã vạch (Barcode) của sản phẩm trong kho
   * @example "1234567890123"
   */
  @ApiProperty({
    description: 'Mã vạch (Barcode) của sản phẩm trong kho',
    example: '1234567890123',
    required: false,
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Mã vạch phải là chuỗi' })
  @MaxLength(100, { message: 'Mã vạch không được vượt quá 100 ký tự' })
  barcode?: string;
}
