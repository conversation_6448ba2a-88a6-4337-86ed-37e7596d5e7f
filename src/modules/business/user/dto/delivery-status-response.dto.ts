import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ShippingStatusEnum } from '@modules/business/enums';

/**
 * DTO cho thông tin lịch sử trạng thái giao hàng
 */
export class DeliveryStatusHistoryDto {
  @ApiProperty({
    description: 'Trạng thái giao hàng',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.PICKED,
  })
  @Expose()
  status: ShippingStatusEnum;

  @ApiProperty({
    description: 'Mô tả trạng thái',
    example: 'Đã lấy hàng',
  })
  @Expose()
  statusDescription: string;

  @ApiProperty({
    description: 'Thời gian cập nhật trạng thái (Unix timestamp)',
    example: 1704067200000,
  })
  @Expose()
  timestamp: number;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON> chú chi tiết',
    example: 'Shipper đã lấy hàng tại địa chỉ người gửi',
    nullable: true,
  })
  @Expose()
  note?: string;

  @ApiPropertyOptional({
    description: 'Vị trí hiện tại',
    example: 'Kho trung chuyển Hà Nội',
    nullable: true,
  })
  @Expose()
  location?: string;
}

/**
 * DTO cho thông tin nhà cung cấp vận chuyển
 */
export class ShippingProviderInfoDto {
  @ApiProperty({
    description: 'Tên nhà cung cấp vận chuyển',
    example: 'GHN',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Mã vận đơn',
    example: 'GHN123456789',
  })
  @Expose()
  trackingNumber: string;

  @ApiPropertyOptional({
    description: 'URL tracking trên website nhà cung cấp',
    example: 'https://tracking.ghn.vn/?order_code=GHN123456789',
    nullable: true,
  })
  @Expose()
  trackingUrl?: string;

  @ApiPropertyOptional({
    description: 'Thông tin liên hệ shipper',
    example: {
      name: 'Nguyễn Văn A',
      phone: '**********'
    },
    nullable: true,
  })
  @Expose()
  shipperInfo?: {
    name?: string;
    phone?: string;
  };
}

/**
 * DTO cho thông tin địa chỉ giao hàng
 */
export class DeliveryAddressDto {
  @ApiProperty({
    description: 'Tên người nhận',
    example: 'Nguyễn Văn B',
  })
  @Expose()
  receiverName: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0987654321',
  })
  @Expose()
  receiverPhone: string;

  @ApiProperty({
    description: 'Địa chỉ giao hàng',
    example: '123 Đường ABC, Phường XYZ, Quận 1, TP.HCM',
  })
  @Expose()
  address: string;

  @ApiPropertyOptional({
    description: 'Ghi chú địa chỉ',
    example: 'Giao hàng tại cổng chính, gọi trước 15 phút',
    nullable: true,
  })
  @Expose()
  note?: string;
}

/**
 * DTO response cho trạng thái giao hàng của đơn hàng
 */
export class DeliveryStatusResponseDto {
  @ApiProperty({
    description: 'ID đơn hàng',
    example: 1,
  })
  @Expose()
  orderId: number;

  @ApiProperty({
    description: 'Trạng thái giao hàng hiện tại',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.DELIVERING,
  })
  @Expose()
  currentStatus: ShippingStatusEnum;

  @ApiProperty({
    description: 'Mô tả trạng thái hiện tại',
    example: 'Đang giao hàng',
  })
  @Expose()
  currentStatusDescription: string;

  @ApiProperty({
    description: 'Đơn hàng có yêu cầu vận chuyển hay không',
    example: true,
  })
  @Expose()
  hasShipping: boolean;

  @ApiProperty({
    description: 'Thời gian cập nhật trạng thái gần nhất (Unix timestamp)',
    example: 1704067200000,
  })
  @Expose()
  lastUpdated: number;

  @ApiProperty({
    description: 'Thời gian dự kiến giao hàng (Unix timestamp)',
    example: 1704153600000,
    nullable: true,
  })
  @Expose()
  estimatedDelivery?: number;

  @ApiProperty({
    description: 'Thông tin địa chỉ giao hàng',
    type: DeliveryAddressDto,
  })
  @Expose()
  @Type(() => DeliveryAddressDto)
  deliveryAddress: DeliveryAddressDto;

  @ApiPropertyOptional({
    description: 'Thông tin nhà cung cấp vận chuyển',
    type: ShippingProviderInfoDto,
    nullable: true,
  })
  @Expose()
  @Type(() => ShippingProviderInfoDto)
  providerInfo?: ShippingProviderInfoDto;

  @ApiPropertyOptional({
    description: 'Lịch sử trạng thái giao hàng',
    type: [DeliveryStatusHistoryDto],
    nullable: true,
  })
  @Expose()
  @Type(() => DeliveryStatusHistoryDto)
  statusHistory?: DeliveryStatusHistoryDto[];

  @ApiPropertyOptional({
    description: 'Thông tin bổ sung từ nhà cung cấp vận chuyển',
    example: {
      weight: 500,
      dimensions: '20x15x10 cm',
      serviceType: 'EXPRESS'
    },
    nullable: true,
  })
  @Expose()
  additionalInfo?: Record<string, any>;
}
