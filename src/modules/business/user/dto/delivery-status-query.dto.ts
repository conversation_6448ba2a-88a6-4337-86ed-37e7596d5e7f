import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho tham số truy vấn trạng thái giao hàng
 */
export class DeliveryStatusQueryDto {
  @ApiPropertyOptional({
    description: '<PERSON><PERSON> bao gồm lịch sử trạng thái chi tiết hay không',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  includeHistory?: boolean = false;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON> bao gồm thông tin nhà cung cấp vận chuyển hay không',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  includeProvider?: boolean = false;
}
