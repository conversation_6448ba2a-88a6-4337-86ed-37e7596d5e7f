import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { SegmentCriteriaDto } from './segment-criteria.dto';

/**
 * DTO cho việc preview segment (tính tổng audience mà không tạo segment)
 */
export class SegmentPreviewDto {
  /**
   * Điều kiện lọc khách hàng
   */
  @ApiProperty({
    description: 'Điều kiện lọc khách hàng',
    type: SegmentCriteriaDto,
  })
  @IsNotEmpty({ message: 'Điều kiện lọc không được để trống' })
  @ValidateNested()
  @Type(() => SegmentCriteriaDto)
  criteria: SegmentCriteriaDto;

  /**
   * ID của segment cần cập nhật số lượ<PERSON> (nếu có)
   */
  @ApiPropertyOptional({
    description: 'ID của segment cần cập nhật số lượng',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  segmentId?: number;
}

/**
 * DTO cho phản hồi preview segment
 */
export class SegmentPreviewResponseDto {
  /**
   * Tổng số audience phù hợp với điều kiện
   * @example 150
   */
  @ApiProperty({
    description: 'Tổng số audience phù hợp với điều kiện',
    example: 150,
  })
  totalAudiences: number;

  /**
   * Tỷ lệ phần trăm so với tổng audience
   * @example 0.75
   */
  @ApiProperty({
    description: 'Tỷ lệ phần trăm so với tổng audience',
    example: 0.75,
  })
  percentageOfTotal: number;

  /**
   * Tổng số audience của người dùng
   * @example 200
   */
  @ApiProperty({
    description: 'Tổng số audience của người dùng',
    example: 200,
  })
  totalUserAudiences: number;
}
