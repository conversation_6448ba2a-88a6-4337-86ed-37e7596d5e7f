import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  getSchemaPath,
} from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { UserSegmentService } from '../services/user-segment.service';
import {
  CreateSegmentDto,
  UpdateSegmentDto,
  SegmentResponseDto,
  SegmentStatsDto,
  SegmentQueryDto,
  SegmentAudienceResponseDto,
  SegmentAudienceQueryDto,
  SegmentPreviewDto,
  SegmentPreviewResponseDto,
} from '../dto/segment';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';

/**
 * Controller xử lý API liên quan đến segment
 */
@ApiTags(SWAGGER_API_TAGS.USER_SEGMENT)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/segments')
export class UserSegmentController {
  constructor(private readonly userSegmentService: UserSegmentService) {}

  /**
   * Tạo segment mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo segment mới' })
  @ApiResponse({
    status: 201,
    description: 'Segment đã được tạo thành công',
    type: SegmentResponseDto,
  })
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() createSegmentDto: CreateSegmentDto,
  ): Promise<AppApiResponse<SegmentResponseDto>> {
    const result = await this.userSegmentService.create(user.id, createSegmentDto);
    return wrapResponse(result, 'Segment đã được tạo thành công');
  }

  /**
   * Preview segment - tính tổng số audience phù hợp với điều kiện
   * Nếu cung cấp segmentId thì sẽ cập nhật số lượng audience vào segment đó
   */
  @Post('preview')
  @ApiOperation({ 
    summary: 'Preview segment - tính tổng số audience phù hợp với điều kiện', 
    description: 'Tính tổng số audience phù hợp với điều kiện. Nếu cung cấp segmentId thì sẽ cập nhật số lượng audience vào segment đó'
  })
  @ApiResponse({
    status: 200,
    description: 'Thống kê số audience phù hợp với điều kiện segment',
    type: SegmentPreviewResponseDto,
  })
  async previewSegment(
    @CurrentUser() user: JwtPayload,
    @Body() previewDto: SegmentPreviewDto,
  ): Promise<AppApiResponse<SegmentPreviewResponseDto>> {
    const result = await this.userSegmentService.previewSegmentAudienceCount(user.id, previewDto);
    return wrapResponse(result, 'Thống kê audience phù hợp với điều kiện segment');
  }

  /**
   * Lấy danh sách segment với phân trang và tìm kiếm
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách segment với phân trang và tìm kiếm' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách segment với phân trang',
    schema: {
      allOf: [
        { $ref: getSchemaPath(AppApiResponse) },
        {
          properties: {
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(SegmentResponseDto) }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: SegmentQueryDto,
  ): Promise<AppApiResponse<PaginatedResult<SegmentResponseDto>>> {
    const result = await this.userSegmentService.findAll(user.id, queryDto);
    return wrapResponse(result, 'Danh sách segment');
  }

  /**
   * Lấy thông tin segment theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin segment theo ID' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin segment',
    type: SegmentResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Segment không tồn tại' })
  async findOne(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<AppApiResponse<SegmentResponseDto>> {
    const result = await this.userSegmentService.findOne(user.id, +id);
    return wrapResponse(result, 'Thông tin segment');
  }

  /**
   * Lấy thống kê của segment
   */
  @Get(':id/stats')
  @ApiOperation({ summary: 'Lấy thống kê của segment' })
  @ApiResponse({
    status: 200,
    description: 'Thống kê segment',
    type: SegmentStatsDto,
  })
  @ApiResponse({ status: 404, description: 'Segment không tồn tại' })
  async getStats(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<AppApiResponse<SegmentStatsDto>> {
    const result = await this.userSegmentService.getStats(user.id, +id);
    return wrapResponse(result, 'Thống kê segment');
  }

  /**
   * Lấy danh sách audience trong segment
   */
  @Get(':id/audiences')
  @ApiOperation({ summary: 'Lấy danh sách audience trong segment' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách audience trong segment với phân trang',
    schema: {
      allOf: [
        { $ref: getSchemaPath(AppApiResponse) },
        {
          properties: {
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(SegmentAudienceResponseDto) }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Segment không tồn tại' })
  async getAudiences(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Query() queryDto: SegmentAudienceQueryDto,
  ): Promise<AppApiResponse<PaginatedResult<SegmentAudienceResponseDto>>> {
    const result = await this.userSegmentService.getSegmentAudiences(user.id, +id, queryDto);
    return wrapResponse(result, 'Danh sách audience trong segment');
  }

  /**
   * Cập nhật segment
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật segment' })
  @ApiResponse({
    status: 200,
    description: 'Segment đã được cập nhật thành công',
    type: SegmentResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Segment không tồn tại' })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateSegmentDto: UpdateSegmentDto,
  ): Promise<AppApiResponse<SegmentResponseDto>> {
    const result = await this.userSegmentService.update(user.id, +id, updateSegmentDto);
    return wrapResponse(result, 'Segment đã được cập nhật thành công');
  }

  /**
   * Xóa segment
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa segment' })
  @ApiResponse({ status: 200, description: 'Segment đã được xóa thành công' })
  @ApiResponse({ status: 404, description: 'Segment không tồn tại' })
  async remove(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<AppApiResponse<{ success: boolean }>> {
    const result = await this.userSegmentService.remove(user.id, +id);
    return wrapResponse(result, 'Segment đã được xóa thành công');
  }

  /**
   * Cập nhật số lượng audience của segment
   */
  @Post(':id/calculate-audience-count')
  @ApiOperation({ summary: 'Tính và cập nhật số lượng audience trong segment' })
  @ApiResponse({
    status: 200,
    description: 'Segment đã được cập nhật với số lượng audience',
    schema: {
      allOf: [
        { $ref: getSchemaPath(AppApiResponse) },
        {
          properties: {
            result: { $ref: getSchemaPath(SegmentResponseDto) },
          },
        },
      ],
    },
  })
  async calculateAudienceCount(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
  ): Promise<AppApiResponse<SegmentResponseDto>> {
    const result = await this.userSegmentService.calculateAndUpdateAudienceCount(user.id, id);
    return wrapResponse(result, 'Số lượng audience trong segment đã được cập nhật');
  }
}
